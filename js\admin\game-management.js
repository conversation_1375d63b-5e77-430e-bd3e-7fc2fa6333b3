// Global variables
let gameLevels = [];
let filteredGameLevels = [];
let gameContent = [];

// API Configuration
const API_BASE_URL = '../../php/admin/';

// Constants
const MAX_QUESTIONS_PER_LEVEL = 50;

// Simplified question management - no more pending changes
let isLoading = false; // Track loading state to prevent concurrent operations

// Track expanded levels to preserve state during refresh
let expandedLevels = new Set(); // Set<levelID>

// Initialize game content data
async function initializeGameData() {
    try {
        await loadGameContent();
        displayGameLevels();
    } catch (error) {
        console.error('Error initializing game data:', error);
        showNotification('Error loading game content from database', 'error');
    }
}

// Load game content from API
async function loadGameContent() {
    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_all_content`);
        const result = await response.json();

        if (result.success) {
            gameContent = result.data;
            processGameLevels();
        } else {
            throw new Error(result.error || 'Failed to load game content');
        }
    } catch (error) {
        console.error('Error loading game content:', error);
        showNotification(error.message || 'Error loading game content', 'error');
        gameContent = [];
        gameLevels = [];
        filteredGameLevels = [];
    }
}

// Process game content into levels
function processGameLevels() {
    const levelGroups = {};

    gameContent.forEach(content => {
        const levelNum = content.level_number;
        if (!levelGroups[levelNum]) {
            // Create level name with title if available
            const title = content.title && content.title.trim() ? content.title.trim() : '';
            const levelName = title ? `Level ${levelNum}: ${title}` : `Level ${levelNum}`;

            levelGroups[levelNum] = {
                levelID: levelNum,
                levelName: levelName,
                title: title,
                canEdit: content.can_edit == 1, // Convert to boolean
                questions: []
            };
        }

        // Update title if this content has a title and the level doesn't have one yet
        if (content.title && content.title.trim() && !levelGroups[levelNum].title) {
            const title = content.title.trim();
            levelGroups[levelNum].title = title;
            levelGroups[levelNum].levelName = `Level ${levelNum}: ${title}`;
        }

        // Update canEdit to false if any question in the level has can_edit = 0
        if (content.can_edit == 0) {
            levelGroups[levelNum].canEdit = false;
        }

        levelGroups[levelNum].questions.push({
            id: content.content_id,
            question: content.question_text,
            options: [content.option1, content.option2, content.option3, content.option4],
            correctAnswer: content.correct_answer,
            canEdit: content.can_edit == 1,
            quizType: content.quiz_type || 'Multiple Choice',
            levelNumber: content.level_number // Store the actual level_number from database
        });
    });

    gameLevels = Object.values(levelGroups).sort((a, b) => a.levelID - b.levelID);
    filteredGameLevels = [...gameLevels];

    if (gameLevels.length === 0) {
        gameLevels = [];
        filteredGameLevels = [];
    }

}

// Get question data by ID and level
function getQuestionData(questionId, levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (level) {
        const question = level.questions.find(q => q.id === questionId);
        return question || null;
    }
    return null;
}

// Update question in local data after successful save
function updateLocalQuestionData(questionId, levelID, newData) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (level) {
        const questionIndex = level.questions.findIndex(q => q.id === questionId);
        if (questionIndex !== -1) {
            level.questions[questionIndex] = {
                ...level.questions[questionIndex],
                ...newData
            };
        }
    }
}

// Display game levels
function displayGameLevels() {
    const levelsContainer = document.getElementById('levelsContainer');
    levelsContainer.innerHTML = '';

    if (filteredGameLevels.length === 0) {
        const isSearching = document.getElementById('levelSearch').value.trim() !== '';
        levelsContainer.innerHTML = `
            <div class="empty-levels-state">
                <div class="empty-icon">
                    <i class="fas fa-${isSearching ? 'search' : 'gamepad'}"></i>
                </div>
                <h3>${isSearching ? 'No Levels Found' : 'No Game Levels Found'}</h3>
                <p>${isSearching ? 'No levels match your search criteria. Try different keywords.' : 'No levels with questions have been created yet. Add some questions to create levels.'}</p>
                ${!isSearching ? '<button class="btn-primary" onclick="showAddQuestionModal(1)"><i class="fas fa-plus"></i> Add First Question</button>' : ''}
            </div>
        `;
        return;
    }

    filteredGameLevels.forEach(level => {
        const levelCard = createLevelCard(level);
        levelsContainer.appendChild(levelCard);
    });
}

// Store the current expanded state of levels
function storeExpandedState() {
    expandedLevels.clear();
    filteredGameLevels.forEach(level => {
        const questionsContainer = document.getElementById(`level-questions-${level.levelID}`);
        if (questionsContainer && questionsContainer.classList.contains('expanded')) {
            expandedLevels.add(level.levelID);
        }
    });
}

// Restore the expanded state of levels
function restoreExpandedState() {
    expandedLevels.forEach(levelID => {
        const questionsContainer = document.getElementById(`level-questions-${levelID}`);
        const dropdownToggle = document.getElementById(`dropdown-toggle-${levelID}`);

        if (questionsContainer && dropdownToggle) {
            questionsContainer.classList.remove('collapsed');
            questionsContainer.classList.add('expanded');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
        }
    });
}

// Create level card HTML
function createLevelCard(level) {
    const card = document.createElement('div');
    card.className = 'level-card';

    const questionsHtml = level.questions.map(question => {
        const currentQuestion = question;

        let optionsHtml = '';
        if ((currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice') {
            const optionLabels = ['A', 'B', 'C', 'D'];
            optionsHtml = currentQuestion.options.map((option, index) => {
                // Debug logging
                console.log('Question:', currentQuestion.question);
                console.log('Correct Answer:', currentQuestion.correctAnswer);
                console.log('Options:', currentQuestion.options);

                let correctIndex = -1;

                // Check if correctAnswer is a letter (A, B, C, D)
                if (typeof currentQuestion.correctAnswer === 'string' &&
                    optionLabels.includes(currentQuestion.correctAnswer.toUpperCase())) {
                    correctIndex = optionLabels.indexOf(currentQuestion.correctAnswer.toUpperCase());
                }
                // Check if correctAnswer is a number (1, 2, 3, 4)
                else if (typeof currentQuestion.correctAnswer === 'number' &&
                         currentQuestion.correctAnswer >= 1 && currentQuestion.correctAnswer <= 4) {
                    correctIndex = currentQuestion.correctAnswer - 1;
                }
                // Check if correctAnswer is a string number ("1", "2", "3", "4")
                else if (typeof currentQuestion.correctAnswer === 'string' &&
                         !isNaN(currentQuestion.correctAnswer) &&
                         parseInt(currentQuestion.correctAnswer) >= 1 &&
                         parseInt(currentQuestion.correctAnswer) <= 4) {
                    correctIndex = parseInt(currentQuestion.correctAnswer) - 1;
                }
                // Check if correctAnswer matches the actual option text
                else if (typeof currentQuestion.correctAnswer === 'string') {
                    correctIndex = currentQuestion.options.findIndex(opt =>
                        opt && opt.toLowerCase().trim() === currentQuestion.correctAnswer.toLowerCase().trim()
                    );
                }

                const isCorrect = index === correctIndex;
                console.log(`Option ${index} (${optionLabels[index]}): "${option}" - Correct: ${isCorrect} (correctIndex: ${correctIndex})`);

                return `<li>
                    <span class="option-label">${optionLabels[index]}:</span>
                    <span class="option-text">${option}</span>${isCorrect ? '<span class="correct-answer">✓ Correct</span>' : ''}
                </li>`;
            }).join('');
        }

        return `<div class="question-item">
            <div class="question-header">
                <div class="question-text">
                    ${currentQuestion.question}
                </div>
                <div class="question-actions">
                    <button class="btn-small btn-warning" onclick="editQuestion(${question.id}, ${level.levelID})" ${!question.canEdit ? 'disabled title="Editing disabled for this question"' : ''}>
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-danger" onclick="deleteQuestion(${question.id})" ${!question.canEdit ? 'disabled title="Deletion disabled for this question"' : ''}>
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="question-meta">
                <span>Level ${level.levelID}</span>
                <span class="quiz-type-label">${currentQuestion.quizType || 'Multiple Choice'}</span>
            </div>
            ${(currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice' ? `<ul class="options-list">${optionsHtml}</ul>` : ''}
            ${(currentQuestion.quizType || 'Multiple Choice') === 'Matching Type' ? `<div class="matching-answer"><b>Correct Answer:</b> ${currentQuestion.correctAnswer}</div>` : ''}
        </div>`;
    }).join('');

    // No more pending changes system

    card.innerHTML = `
        <div class="level-header" onclick="showLevelQuestionsModal(${level.levelID})" style="cursor: pointer;" title="Click to view questions in this level">
            <div class="level-info">
                <div class="level-title-row">
                    <h3>${level.levelName} ${!level.canEdit ? '<span class="edit-disabled-indicator" title="Editing disabled"><i class="fas fa-lock"></i></span>' : ''}</h3>
                    <div class="level-meta">
                        <span class="questions-count">${level.questions.length}/${MAX_QUESTIONS_PER_LEVEL} questions</span>
                        ${!level.canEdit ? '<span class="edit-disabled-text">Read-only</span>' : ''}
                        ${level.questions.length >= MAX_QUESTIONS_PER_LEVEL ? '<span class="limit-reached-text">Limit reached</span>' : ''}
                    </div>
                </div>
            </div>
            <div class="level-header-actions" onclick="event.stopPropagation()">

                ${level.canEdit ? `
                    <button class="btn-small btn-primary" onclick="showAddQuestionModal(${level.levelID})" title="${level.questions.length >= MAX_QUESTIONS_PER_LEVEL ? 'Maximum questions reached' : 'Add new question'}" ${level.questions.length >= MAX_QUESTIONS_PER_LEVEL ? 'disabled' : ''}>
                        <i class="fas fa-plus"></i> Add Question
                    </button>
                    <button class="btn-small btn-danger" onclick="showDeleteLevelModal(${level.levelID})" title="Delete this level">
                        <i class="fas fa-trash"></i> Delete Level
                    </button>
                ` : ''}
                <button class="btn-small btn-info" onclick="showLevelQuestionsModal(${level.levelID})" title="View questions in modal">
                    <i class="fas fa-eye"></i> View Questions
                </button>
            </div>
        </div>
    `;

    return card;
}

// Search levels
function searchLevels() {
    const searchTerm = document.getElementById('levelSearch').value.toLowerCase();

    if (searchTerm.trim() === '') {
        filteredGameLevels = [...gameLevels];
    } else {
        filteredGameLevels = gameLevels.filter(level => {
            return level.levelID.toString().includes(searchTerm);
        });
    }

    displayGameLevels();
}

// Show Level Questions Modal
let currentLevelID = null;

function showLevelQuestionsModal(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) {
        showNotification('Level not found', 'error');
        return;
    }

    currentLevelID = levelID;

    // Update modal title and subtitle
    document.getElementById('levelModalTitle').textContent = `${level.levelName}`;
    document.getElementById('levelModalSubtitle').textContent = `Manage questions for this level`;

    // Update question count
    document.getElementById('levelQuestionCount').textContent = `${level.questions.length}/${MAX_QUESTIONS_PER_LEVEL}`;

    // Display level questions
    displayLevelQuestions(level);

    // Update add question button
    const addBtn = document.getElementById('addQuestionFromLevelBtn2');

    if (level.canEdit) {
        addBtn.style.display = 'inline-flex';
        if (level.questions.length >= MAX_QUESTIONS_PER_LEVEL) {
            addBtn.disabled = true;
            addBtn.title = `Maximum questions reached (${MAX_QUESTIONS_PER_LEVEL})`;
        } else {
            addBtn.disabled = false;
            addBtn.title = 'Add new question';
        }
        addBtn.onclick = () => showAddQuestionModalFromLevel();
    } else {
        addBtn.style.display = 'none';
    }

    // Show modal
    openModal('levelQuestionsModal');
}



// Display level questions in modal
function displayLevelQuestions(level) {
    const container = document.getElementById('levelQuestionsContainer');
    container.innerHTML = '';

    if (level.questions.length === 0) {
        container.innerHTML = `
            <div class="empty-level-questions">
                <div style="text-align: center; padding: 40px; color: rgba(255, 255, 255, 0.6);">
                    <i class="fas fa-question-circle" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                    <h3 style="margin-bottom: 10px; color: rgba(255, 255, 255, 0.8);">No Questions Yet</h3>
                    <p style="margin: 0;">
                        ${level.canEdit ?
                            'Click "Add Question" to get started with this level.' :
                            'No questions are available in this level.'
                        }
                    </p>
                </div>
            </div>
        `;
        updateFooterInfo(level);
        return;
    }

    // Create question items
    level.questions.forEach((question, index) => {
        const questionItem = createLevelQuestionItem(question, level, index);
        container.appendChild(questionItem);
    });

    updateFooterInfo(level);
}

// Update footer information
function updateFooterInfo(level) {
    const footerInfo = document.getElementById('questionsFooterInfo');
    if (level.questions.length === 0) {
        footerInfo.textContent = 'No questions added yet';
    } else {
        footerInfo.textContent = `${level.questions.length} question${level.questions.length !== 1 ? 's' : ''} in this level`;
    }
}

// Create level question item element
function createLevelQuestionItem(question, level, questionIndex) {
    const currentQuestion = question;

    const item = document.createElement('div');
    item.className = 'level-question-item';
    item.setAttribute('data-question-id', question.id);

    let optionsHtml = '';
    if ((currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice') {
        const optionLabels = ['A', 'B', 'C', 'D'];
        optionsHtml = `
            <div class="question-options">
                ${currentQuestion.options.map((option, index) => {
                    const isCorrect = isCorrectOption(currentQuestion, index);
                    return `<div class="question-option ${isCorrect ? 'correct' : ''}">
                        <strong>${optionLabels[index]}.</strong> ${option}
                    </div>`;
                }).join('')}
            </div>
        `;
    } else {
        optionsHtml = `
            <div class="question-options">
                <div class="question-option correct">
                    <strong>Correct Answer:</strong> ${currentQuestion.correctAnswer}
                </div>
            </div>
        `;
    }

    const questionTypeClass = (currentQuestion.quizType || 'Multiple Choice') === 'Matching Type' ? 'matching' : '';

    item.innerHTML = `
        <div class="level-question-header">
            <div class="level-question-text">
                <div class="question-number">${questionIndex + 1}</div>
                <div class="question-text-content">
                    ${currentQuestion.question}
                </div>
            </div>
            <div class="level-question-actions">
                ${question.canEdit ? `
                    <button class="btn-small btn-warning" onclick="editQuestion(${question.id}, ${level.levelID})" title="Edit question">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-danger" onclick="deleteQuestion(${question.id})" title="Delete question">
                        <i class="fas fa-trash"></i>
                    </button>
                ` : ''}
            </div>
        </div>
        <div class="question-content" id="question-content-${question.id}">
            <div class="question-type-badge ${questionTypeClass}">
                <i class="fas ${(currentQuestion.quizType || 'Multiple Choice') === 'Matching Type' ? 'fa-link' : 'fa-list-ul'}"></i>
                ${currentQuestion.quizType || 'Multiple Choice'}
            </div>

            ${optionsHtml}
        </div>
    `;

    return item;
}

// Show add question modal from level modal
function showAddQuestionModalFromLevel() {
    if (currentLevelID) {
        closeModal('levelQuestionsModal');
        showAddQuestionModal(currentLevelID);
    }
}



// Show All Questions Modal
function showAllQuestionsModal() {
    // Populate statistics
    updateQuestionsStatistics();

    // Populate level filter
    populateLevelFilter();

    // Display all questions
    displayAllQuestions();

    // Clear filters
    document.getElementById('questionsSearch').value = '';
    document.getElementById('levelFilter').value = '';
    document.getElementById('typeFilter').value = '';

    // Show modal
    openModal('allQuestionsModal');
}

// Update questions statistics
function updateQuestionsStatistics() {
    const totalLevels = gameLevels.length;
    let totalQuestions = 0;

    gameLevels.forEach(level => {
        totalQuestions += level.questions.length;
    });

    document.getElementById('totalLevels').textContent = totalLevels;
    document.getElementById('totalQuestions').textContent = totalQuestions;
}

// Populate level filter dropdown
function populateLevelFilter() {
    const levelFilter = document.getElementById('levelFilter');
    levelFilter.innerHTML = '<option value="">All Levels</option>';

    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    sortedLevels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.levelID;
        option.textContent = `Level ${level.levelID}`;
        levelFilter.appendChild(option);
    });
}

// Display all questions
function displayAllQuestions() {
    const container = document.getElementById('allQuestionsContainer');
    container.innerHTML = '';

    // Collect all questions from all levels
    const allQuestions = [];
    gameLevels.forEach(level => {
        level.questions.forEach(question => {
            allQuestions.push({
                ...question,
                levelID: level.levelID,
                levelName: level.levelName
            });
        });
    });

    // Sort questions by level and then by question ID
    allQuestions.sort((a, b) => {
        if (a.levelID !== b.levelID) {
            return a.levelID - b.levelID;
        }
        return a.id - b.id;
    });

    if (allQuestions.length === 0) {
        container.innerHTML = '<div class="no-questions-found">No questions found</div>';
        return;
    }

    // Create question cards
    allQuestions.forEach(question => {
        const questionCard = createQuestionCard(question);
        container.appendChild(questionCard);
    });

    // Update visible questions count
    updateVisibleQuestionsCount(allQuestions.length);
}

// Create question card element
function createQuestionCard(question) {
    const card = document.createElement('div');
    card.className = 'question-card';
    card.dataset.levelId = question.levelID;
    card.dataset.questionType = question.quizType || 'Multiple Choice';
    card.dataset.questionText = question.question.toLowerCase();

    let contentHtml = '';
    if ((question.quizType || 'Multiple Choice') === 'Multiple Choice') {
        const optionLabels = ['A', 'B', 'C', 'D'];
        contentHtml = `
            <div class="question-content">
                <div class="question-text-display">
                    <p>${question.question}</p>
                </div>
                <div class="question-options">
                    <ul class="question-options-list">
                        ${question.options.map((option, index) => {
                            const isCorrect = isCorrectOption(question, index);
                            return `<li class="${isCorrect ? 'correct-option' : ''}">
                                <span class="option-label">${optionLabels[index]}</span>
                                <span class="option-text">${option}</span>
                                ${isCorrect ? '<i class="fas fa-check-circle" style="color: #27ae60; margin-left: auto;"></i>' : ''}
                            </li>`;
                        }).join('')}
                    </ul>
                </div>
            </div>
        `;
    } else {
        contentHtml = `
            <div class="question-content">
                <div class="question-text-display">
                    <p>${question.question}</p>
                </div>
                <div class="matching-answer">
                    <div style="background: linear-gradient(135deg, rgba(39, 174, 96, 0.2), rgba(39, 174, 96, 0.1));
                                border: 1px solid rgba(39, 174, 96, 0.3);
                                border-radius: 12px;
                                padding: 15px 20px;
                                display: flex;
                                align-items: center;
                                gap: 10px;">
                        <i class="fas fa-bullseye" style="color: #27ae60; font-size: 1.2rem;"></i>
                        <strong style="color: #27ae60;">Correct Answer:</strong>
                        <span style="color: rgba(255, 255, 255, 0.9); font-weight: 500;">${question.correctAnswer}</span>
                    </div>
                </div>
            </div>
        `;
    }

    card.innerHTML = `
        <div class="question-card-header">
            <div class="question-card-title">Question #${question.id}</div>
            <div class="question-card-meta">
                <span class="level-badge">Level ${question.levelID}</span>
                <span class="type-badge">${question.quizType || 'Multiple Choice'}</span>
            </div>
        </div>
        ${contentHtml}
    `;

    return card;
}

// Helper function to determine if an option is correct
function isCorrectOption(question, optionIndex) {
    const optionLabels = ['A', 'B', 'C', 'D'];
    const correctAnswer = question.correctAnswer;

    // Check if correctAnswer is a letter (A, B, C, D)
    if (typeof correctAnswer === 'string' && optionLabels.includes(correctAnswer.toUpperCase())) {
        return optionLabels.indexOf(correctAnswer.toUpperCase()) === optionIndex;
    }

    // Check if correctAnswer is a number (1, 2, 3, 4)
    if (typeof correctAnswer === 'number' && correctAnswer >= 1 && correctAnswer <= 4) {
        return correctAnswer - 1 === optionIndex;
    }

    // Check if correctAnswer is a string number ("1", "2", "3", "4")
    if (typeof correctAnswer === 'string' && !isNaN(correctAnswer)) {
        const num = parseInt(correctAnswer);
        if (num >= 1 && num <= 4) {
            return num - 1 === optionIndex;
        }
    }

    // Check if correctAnswer matches the actual option text
    if (typeof correctAnswer === 'string') {
        return question.options[optionIndex] &&
               question.options[optionIndex].toLowerCase().trim() === correctAnswer.toLowerCase().trim();
    }

    return false;
}

// Filter all questions based on search and filters
function filterAllQuestions() {
    const searchTerm = document.getElementById('questionsSearch').value.toLowerCase();
    const levelFilter = document.getElementById('levelFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    const questionCards = document.querySelectorAll('.question-card');
    let visibleCount = 0;

    questionCards.forEach(card => {
        const questionText = card.dataset.questionText;
        const questionLevel = card.dataset.levelId;
        const questionType = card.dataset.questionType;

        const matchesSearch = !searchTerm || questionText.includes(searchTerm);
        const matchesLevel = !levelFilter || questionLevel === levelFilter;
        const matchesType = !typeFilter || questionType === typeFilter;

        if (matchesSearch && matchesLevel && matchesType) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // Show "no questions found" message if no results
    const container = document.getElementById('allQuestionsContainer');
    const existingNoResults = container.querySelector('.no-questions-found');

    if (visibleCount === 0 && !existingNoResults) {
        const noResults = document.createElement('div');
        noResults.className = 'no-questions-found';
        noResults.textContent = 'No questions match your search criteria';
        container.appendChild(noResults);
    } else if (visibleCount > 0 && existingNoResults) {
        existingNoResults.remove();
    }

    // Update visible questions count
    updateVisibleQuestionsCount(visibleCount);
}

// Update visible questions count in footer
function updateVisibleQuestionsCount(count) {
    const countElement = document.getElementById('visibleQuestionsCount');
    if (countElement) {
        countElement.textContent = count;
    }
}

// Utility: Show/hide option fields based on quiz_type
function updateModalFieldsByQuizType(quizType) {
    const optionFields = document.querySelectorAll('.option-field');
    const correctAnswerInput = document.getElementById('correctAnswer');
    const correctAnswerLabel = correctAnswerInput.previousElementSibling;
    const infoText = correctAnswerInput.nextElementSibling;
    if (quizType === 'Multiple Choice') {
        optionFields.forEach(f => f.style.display = 'block');
        correctAnswerLabel.textContent = 'Correct Answer:';
        correctAnswerInput.placeholder = 'Type the exact answer here';
        if (infoText) infoText.style.display = 'block';
    } else {
        optionFields.forEach(f => f.style.display = 'none');
        correctAnswerLabel.textContent = 'Correct Answers:';
        correctAnswerInput.placeholder = 'Type the correct answer(s) here';
        if (infoText) infoText.style.display = 'none';
    }
}

// Add event listener for quizType select
if (document.getElementById('quizType')) {
    document.getElementById('quizType').addEventListener('change', function() {
        updateModalFieldsByQuizType(this.value);
    });
}

// Show add question modal
function showAddQuestionModal(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level || !level.canEdit) {
        showNotification('Adding questions is disabled for this level', 'error');
        return;
    }

    // Check if level has reached the maximum question limit
    if (level.questions.length >= MAX_QUESTIONS_PER_LEVEL) {
        showNotification(`Cannot add more questions. Level ${levelID} has reached the maximum limit of ${MAX_QUESTIONS_PER_LEVEL} questions.`, 'error');
        return;
    }

    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Add Question';
    document.getElementById('questionForm').reset(); // Reset first



    // Set level number and make it editable for new questions
    const levelNumberField = document.getElementById('levelNumber');

    console.log('Add Question Debug:', {
        levelID: levelID,
        levelNumberField: levelNumberField,
        fieldExists: !!levelNumberField
    });

    if (levelNumberField) {
        levelNumberField.value = levelID;
        levelNumberField.readOnly = false;
        levelNumberField.style.backgroundColor = '';
        levelNumberField.style.cursor = '';
        levelNumberField.style.display = 'block';
        levelNumberField.style.visibility = 'visible';
        levelNumberField.style.opacity = '1';
        console.log('Level number field value set to:', levelNumberField.value);
    } else {
        console.error('Level number field not found in add question modal!');
    }

    document.getElementById('contentId').value = '';
    document.getElementById('quizType').value = 'Multiple Choice';
    document.getElementById('levelTitle').value = level.title || '';
    updateModalFieldsByQuizType('Multiple Choice');

    // Hide auto-save status and save button for new questions, show Add button
    const autoSaveStatus = document.getElementById('autoSaveStatus');
    const submitBtn = document.getElementById('submitQuestionBtn');
    const saveBtn = document.getElementById('saveQuestionBtn');

    if (autoSaveStatus) {
        autoSaveStatus.style.display = 'none';
    }
    if (submitBtn) {
        submitBtn.style.display = 'inline-block';
        submitBtn.innerHTML = '<i class="fas fa-plus"></i> Add Question';
    }
    if (saveBtn) {
        saveBtn.style.display = 'none'; // Hide Save button when adding new questions
    }

    modal.classList.add('show');

    // Initialize level number input and test field visibility after modal opens
    setTimeout(() => {
        initializeLevelNumberInput();
        testLevelNumberField();
        // Update the display with the current value
        const levelNumberValue = document.getElementById('levelNumberValue');
        const levelNumber = document.getElementById('levelNumber');
        if (levelNumberValue && levelNumber) {
            levelNumberValue.textContent = levelNumber.value || '1';
        }
    }, 100);
}

// Edit question
function editQuestion(questionId, levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    // Check if editing is allowed for this level
    if (!level.canEdit) {
        showNotification('Editing is disabled for this level', 'error');
        return;
    }

    // Get question data
    const currentQuestion = getQuestionData(questionId, levelID);
    if (!currentQuestion) {
        showNotification('Question not found', 'error');
        return;
    }

    // Check if editing is allowed for this specific question
    if (!currentQuestion.canEdit) {
        showNotification('Editing is disabled for this question', 'error');
        return;
    }

    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Edit Question (Change level number to move question)';
    document.getElementById('contentId').value = questionId;
    document.getElementById('questionText').value = currentQuestion.question;

    // Allow level number changes to move questions between levels
    const levelNumberField = document.getElementById('levelNumber');
    // Use the actual level_number from the database, not the levelID
    const actualLevelNumber = currentQuestion.levelNumber || levelID;

    console.log('Edit Question Debug:', {
        questionId: questionId,
        levelID: levelID,
        currentQuestion: currentQuestion,
        actualLevelNumber: actualLevelNumber,
        levelNumberField: levelNumberField,
        fieldExists: !!levelNumberField,
        fieldVisible: levelNumberField ? getComputedStyle(levelNumberField).display : 'N/A'
    });

    if (levelNumberField) {
        levelNumberField.value = actualLevelNumber;
        // Ensure the field is visible and properly styled
        levelNumberField.style.display = 'block';
        levelNumberField.style.visibility = 'visible';
        levelNumberField.style.opacity = '1';
        levelNumberField.readOnly = false;
        levelNumberField.style.backgroundColor = '';
        levelNumberField.style.cursor = '';
        console.log('Level number field value set to:', levelNumberField.value);
    } else {
        console.error('Level number field not found!');
    }

    // Add change listener to show warning when level number is changed
    levelNumberField.addEventListener('change', function() {
        const newLevel = parseInt(this.value);
        const originalLevel = actualLevelNumber;
        if (newLevel !== originalLevel) {
            showNotification(`Question will be moved from Level ${originalLevel} to Level ${newLevel} when saved`, 'info');
        }
    });

    // Set quizType and update fields
    document.getElementById('quizType').value = currentQuestion.quizType || 'Multiple Choice';
    updateModalFieldsByQuizType(document.getElementById('quizType').value);
    if ((currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice') {
        document.getElementById('option1').value = currentQuestion.options[0];
        document.getElementById('option2').value = currentQuestion.options[1];
        document.getElementById('option3').value = currentQuestion.options[2];
        document.getElementById('option4').value = currentQuestion.options[3];
    } else {
        document.getElementById('option1').value = '';
        document.getElementById('option2').value = '';
        document.getElementById('option3').value = '';
        document.getElementById('option4').value = '';
    }
    document.getElementById('correctAnswer').value = currentQuestion.correctAnswer;
    document.getElementById('levelTitle').value = level.title || '';

    // Show save button and hide Add button for editing
    const autoSaveStatus = document.getElementById('autoSaveStatus');
    const submitBtn = document.getElementById('submitQuestionBtn');
    const saveBtn = document.getElementById('saveQuestionBtn');

    if (autoSaveStatus) {
        autoSaveStatus.style.display = 'none';
    }
    if (submitBtn) {
        submitBtn.style.display = 'none'; // Hide Add button when editing
    }
    if (saveBtn) {
        saveBtn.style.display = 'inline-flex'; // Show Save button when editing
        saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Changes';
    }

    modal.classList.add('show');

    // Initialize level number input and test field visibility after modal opens
    setTimeout(() => {
        initializeLevelNumberInput();
        testLevelNumberField();
        // Update the display with the current value
        const levelNumberValue = document.getElementById('levelNumberValue');
        const levelNumber = document.getElementById('levelNumber');
        if (levelNumberValue && levelNumber) {
            levelNumberValue.textContent = levelNumber.value || actualLevelNumber;
        }
    }, 100);
}

// Helper function to get current level of a question from database
async function getCurrentQuestionLevelFromDB(questionId) {
    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_content_by_id&content_id=${questionId}`);
        const result = await response.json();

        if (result.success && result.data && result.data.length > 0) {
            return result.data[0].level_number;
        }
    } catch (error) {
        console.error('Error getting question level from database:', error);
    }
    return null;
}

// Helper function to get current level of a question
function getCurrentQuestionLevel(questionId) {
    for (const level of gameLevels) {
        const question = level.questions.find(q => q.id == questionId);
        if (question) {
            // Return the actual level_number from database, not the levelID
            return question.levelNumber || level.levelID;
        }
    }
    return null;
}

// Test function to check level number field visibility
function testLevelNumberField() {
    const levelNumberField = document.getElementById('levelNumber');
    const levelTitleField = document.getElementById('levelTitle');

    console.log('Level Number Field Test:', {
        levelNumberField: levelNumberField,
        levelNumberExists: !!levelNumberField,
        levelNumberValue: levelNumberField ? levelNumberField.value : 'N/A',
        levelNumberDisplay: levelNumberField ? getComputedStyle(levelNumberField).display : 'N/A',
        levelNumberVisibility: levelNumberField ? getComputedStyle(levelNumberField).visibility : 'N/A',
        levelNumberOpacity: levelNumberField ? getComputedStyle(levelNumberField).opacity : 'N/A',
        levelTitleField: levelTitleField,
        levelTitleExists: !!levelTitleField,
        levelTitleValue: levelTitleField ? levelTitleField.value : 'N/A'
    });

    if (levelNumberField) {
        levelNumberField.style.border = '3px solid red';
        levelNumberField.style.backgroundColor = 'yellow';
        levelNumberField.style.color = 'black';
        setTimeout(() => {
            levelNumberField.style.border = '';
            levelNumberField.style.backgroundColor = '';
            levelNumberField.style.color = '';
        }, 3000);
    }
}

// Save question changes manually
async function saveQuestionChanges() {
    const contentId = document.getElementById('contentId').value;
    if (!contentId) {
        showNotification('No question selected for editing', 'error');
        return;
    }

    const saveBtn = document.getElementById('saveQuestionBtn');
    const autoSaveStatus = document.getElementById('autoSaveStatus');

    // Show saving status
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    }
    if (autoSaveStatus) {
        autoSaveStatus.style.display = 'block';
    }

    try {
        // Get the current and new level numbers to check if question is being moved
        const currentLevelID = await getCurrentQuestionLevelFromDB(contentId);
        const newLevelNumber = parseInt(document.getElementById('levelNumber').value);

        await updateQuestionImmediately();

        // Show appropriate success message
        if (currentLevelID && currentLevelID !== newLevelNumber) {
            showNotification(`Question moved from Level ${currentLevelID} to Level ${newLevelNumber} successfully`, 'success');
        } else {
            showNotification('Question saved successfully', 'success');
        }

        // Close modal after successful save
        closeModal('questionModal');

        // Refresh the display
        await loadGameContent();
        displayGameLevels();
    } catch (error) {
        console.error('Error saving question:', error);
        showNotification('Failed to save question changes', 'error');
    } finally {
        // Reset button state
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i> Save';
        }
        if (autoSaveStatus) {
            autoSaveStatus.style.display = 'none';
        }
    }
}

// Level Number Checkbox-Style Input Functions
function adjustLevelNumber(change) {
    const levelNumberInput = document.getElementById('levelNumber');
    const levelNumberValue = document.getElementById('levelNumberValue');
    const levelNumberTextInput = document.getElementById('levelNumberInput');

    let currentValue = parseInt(levelNumberInput.value) || 1;
    let newValue = Math.max(1, currentValue + change);

    // Update all related elements
    levelNumberInput.value = newValue;
    levelNumberValue.textContent = newValue;
    levelNumberTextInput.value = '';

    // Trigger change event for any listeners
    levelNumberInput.dispatchEvent(new Event('change'));
}

function updateLevelNumberFromInput() {
    const levelNumberInput = document.getElementById('levelNumber');
    const levelNumberValue = document.getElementById('levelNumberValue');
    const levelNumberTextInput = document.getElementById('levelNumberInput');

    let inputValue = levelNumberTextInput.value.trim();

    // Only allow numeric input
    if (inputValue && /^\d+$/.test(inputValue)) {
        let newValue = Math.max(1, parseInt(inputValue));

        // Update all related elements
        levelNumberInput.value = newValue;
        levelNumberValue.textContent = newValue;
        levelNumberTextInput.value = '';

        // Trigger change event for any listeners
        levelNumberInput.dispatchEvent(new Event('change'));
    } else if (inputValue) {
        // Invalid input - clear the text input
        levelNumberTextInput.value = '';
        showNotification('Please enter a valid number', 'error');
    }
}

function initializeLevelNumberInput() {
    const levelNumberTextInput = document.getElementById('levelNumberInput');
    const levelNumberDisplay = document.getElementById('levelNumberDisplay');

    if (levelNumberTextInput) {
        // Handle input validation - only allow numbers
        levelNumberTextInput.addEventListener('input', function(e) {
            let value = e.target.value;
            // Remove any non-numeric characters
            e.target.value = value.replace(/[^0-9]/g, '');
        });

        // Handle Enter key to update value
        levelNumberTextInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                updateLevelNumberFromInput();
            }
        });

        // Handle blur to update value
        levelNumberTextInput.addEventListener('blur', function() {
            updateLevelNumberFromInput();
        });
    }

    if (levelNumberDisplay) {
        // Click on display to focus text input
        levelNumberDisplay.addEventListener('click', function() {
            levelNumberTextInput.focus();
        });
    }
}

// Setup immediate update listeners for question editing - DISABLED for manual save
// function setupImmediateUpdateListeners() {
//     const fields = ['questionText', 'option1', 'option2', 'option3', 'option4', 'correctAnswer', 'quizType'];
//     let updateTimeout;

//     fields.forEach(fieldId => {
//         const field = document.getElementById(fieldId);
//         if (field) {
//             field.addEventListener('input', () => {
//                 const contentId = document.getElementById('contentId').value;
//                 if (contentId) { // Only update if we're editing an existing question
//                     clearTimeout(updateTimeout);
//                     updateTimeout = setTimeout(() => {
//                         updateQuestionImmediately();
//                     }, 500); // Debounce updates by 500ms
//                 }
//             });

//             field.addEventListener('change', () => {
//                 const contentId = document.getElementById('contentId').value;
//                 if (contentId) { // Only update if we're editing an existing question
//                     clearTimeout(updateTimeout);
//                     updateQuestionImmediately();
//                 }
//             });
//         }
//     });
// }

// Handle add/edit question form submission - Direct save approach (kept for adding new questions)
async function handleAddQuestion(event) {
    event.preventDefault();

    if (isLoading) {
        showNotification('Please wait, another operation is in progress...', 'warning');
        return;
    }

    const quizType = document.getElementById('quizType').value;
    const questionText = document.getElementById('questionText').value.trim();
    const correctAnswer = document.getElementById('correctAnswer').value.trim();
    const levelNumber = parseInt(document.getElementById('levelNumber').value);
    const contentId = document.getElementById('contentId').value;

    // Basic validation
    if (!questionText) {
        showNotification('Question text is required', 'error');
        return;
    }
    if (!correctAnswer) {
        showNotification('Correct answer is required', 'error');
        return;
    }
    if (!levelNumber || levelNumber < 1) {
        showNotification('Valid level number is required', 'error');
        return;
    }

    let option1 = '', option2 = '', option3 = '', option4 = '';
    if (quizType === 'Multiple Choice') {
        option1 = document.getElementById('option1').value.trim();
        option2 = document.getElementById('option2').value.trim();
        option3 = document.getElementById('option3').value.trim();
        option4 = document.getElementById('option4').value.trim();

        // Validation for Multiple Choice
        if (!option1 || !option2 || !option3 || !option4) {
            showNotification('All four options are required for Multiple Choice questions', 'error');
            return;
        }
        if (![option1, option2, option3, option4].includes(correctAnswer)) {
            showNotification('The correct answer must exactly match one of the options above', 'error');
            return;
        }
    }

    const isEdit = contentId !== '';

    // For editing, we now use immediate updates, so this function only handles new questions
    if (isEdit) {
        showNotification('Question editing is now automatic. Changes are saved as you type.', 'info');
        return;
    }

    isLoading = true;

    try {
        // Add new question directly
        const levelTitle = document.getElementById('levelTitle').value.trim();
        await addQuestionDirectly({
            quiz_type: quizType,
            question_text: questionText,
            option1: option1,
            option2: option2,
            option3: option3,
            option4: option4,
            correct_answer: correctAnswer,
            level_number: levelNumber,
            title: levelTitle
        });

        closeModal('questionModal');
        await loadGameContent(); // Refresh data from database
        displayGameLevels(); // Update display
        expandedLevels.add(levelNumber); // Keep level expanded

    } catch (error) {
        console.error('Error saving question:', error);
        showNotification(error.message || 'Failed to save question', 'error');
    } finally {
        isLoading = false;
    }
}

// Direct database operations - no more pending changes

// Update question directly in database
async function updateQuestionDirectly(contentId, questionData) {
    const updateData = {
        content_id: contentId,
        question_text: questionData.question_text,
        option1: questionData.option1 || '',
        option2: questionData.option2 || '',
        option3: questionData.option3 || '',
        option4: questionData.option4 || '',
        correct_answer: questionData.correct_answer,
        level_number: questionData.level_number,
        quiz_type: questionData.quiz_type || 'Multiple Choice'
    };



    const response = await fetch(`${API_BASE_URL}game_content_api.php?action=update_content`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        throw new Error(`Server returned non-JSON response: ${textResponse.substring(0, 200)}...`);
    }

    const result = await response.json();
    if (!result.success) {
        throw new Error(result.error || 'Failed to update question');
    }

    showNotification('Question updated successfully!', 'success');
    return result;
}

// Add question directly to database
async function addQuestionDirectly(questionData) {
    const formData = new FormData();
    formData.append('quiz_type', questionData.quiz_type);
    formData.append('question_text', questionData.question_text);
    formData.append('option1', questionData.option1);
    formData.append('option2', questionData.option2);
    formData.append('option3', questionData.option3);
    formData.append('option4', questionData.option4);
    formData.append('correct_answer', questionData.correct_answer);
    formData.append('level_number', questionData.level_number);



    const response = await fetch(`${API_BASE_URL}game_content_api.php?action=add_content`, {
        method: 'POST',
        body: formData
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    if (!result.success) {
        throw new Error(result.error || 'Failed to add question');
    }

    showNotification('Question added successfully!', 'success');
    return result;
}

// Update question immediately when editing
async function updateQuestionImmediately() {
    const contentId = document.getElementById('contentId').value;
    if (!contentId) return; // Only update existing questions

    const autoSaveStatus = document.getElementById('autoSaveStatus');

    try {
        // Show saving indicator
        if (autoSaveStatus) {
            autoSaveStatus.style.display = 'flex';
            autoSaveStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        }

        const quizType = document.getElementById('quizType').value;
        const questionText = document.getElementById('questionText').value.trim();
        const correctAnswer = document.getElementById('correctAnswer').value.trim();
        const levelNumber = parseInt(document.getElementById('levelNumber').value);

        // Validate level number
        if (!levelNumber || levelNumber < 1) {
            if (autoSaveStatus) {
                autoSaveStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Invalid level number';
                autoSaveStatus.style.color = '#e74c3c';
            }
            return;
        }

        // Basic validation
        if (!questionText) {
            if (autoSaveStatus) {
                autoSaveStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Question text required';
                autoSaveStatus.style.color = '#e74c3c';
            }
            return;
        }

        let option1 = '', option2 = '', option3 = '', option4 = '';
        if (quizType === 'Multiple Choice') {
            option1 = document.getElementById('option1').value.trim();
            option2 = document.getElementById('option2').value.trim();
            option3 = document.getElementById('option3').value.trim();
            option4 = document.getElementById('option4').value.trim();

            if (!option1 || !option2 || !option3 || !option4) {
                if (autoSaveStatus) {
                    autoSaveStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> All options required';
                    autoSaveStatus.style.color = '#e74c3c';
                }
                return;
            }
        }

        if (!correctAnswer) {
            if (autoSaveStatus) {
                autoSaveStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Correct answer required';
                autoSaveStatus.style.color = '#e74c3c';
            }
            return;
        }

        // Update the question
        const levelTitle = document.getElementById('levelTitle').value.trim();
        await updateQuestionDirectly(parseInt(contentId), {
            question_text: questionText,
            option1: option1,
            option2: option2,
            option3: option3,
            option4: option4,
            correct_answer: correctAnswer,
            level_number: levelNumber,
            quiz_type: quizType,
            title: levelTitle
        });

        // Show success indicator
        if (autoSaveStatus) {
            autoSaveStatus.innerHTML = '<i class="fas fa-check"></i> Saved';
            autoSaveStatus.style.color = '#27ae60';

            // Hide after 2 seconds
            setTimeout(() => {
                autoSaveStatus.style.display = 'none';
                autoSaveStatus.style.color = '';
            }, 2000);
        }

        // Update the display without closing modal
        await loadGameContent();
        displayGameLevels();

    } catch (error) {
        console.error('Error updating question:', error);
        if (autoSaveStatus) {
            autoSaveStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Save failed';
            autoSaveStatus.style.color = '#e74c3c';
        }
    }
}

// Deprecated functions removed - using direct save approach now



// Delete question
async function deleteQuestion(questionId) {
    // Find the question and check if deletion is allowed
    let canDelete = false;
    let levelID = null;

    for (const level of gameLevels) {
        const question = level.questions.find(q => q.id === questionId);
        if (question) {
            levelID = level.levelID;
            canDelete = level.canEdit && question.canEdit;
            break;
        }
    }

    if (!canDelete) {
        showNotification('Deletion is disabled for this question', 'error');
        return;
    }

    if (confirm('Are you sure you want to delete this question?')) {
        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=delete_content&content_id=${questionId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                showNotification('Question deleted successfully', 'success');

                // Refresh data from database to ensure consistency
                await loadGameContent();
                displayGameLevels();
            } else {
                throw new Error(result.error || 'Failed to delete question');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification(error.message || 'An error occurred', 'error');
        }
    }
}

// Open modal
function openModal(modalId) {
    document.getElementById(modalId).classList.add('show');
}

// Close modal
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeGameData();
    initializeLevelNumberInput();
});

// Add form submission and input event listeners
const questionForm = document.getElementById('questionForm');
if (questionForm) {
    // Handle form submission for new questions
    questionForm.addEventListener('submit', handleAddQuestion);

    // Auto-save functionality disabled - using manual save button instead
    // setupImmediateUpdateListeners();
}

// Level Management Functions

// Show Add Level Modal
async function showAddLevelModal() {
    try {
        // Get next available level number
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=get_next_level`);
        const result = await response.json();

        if (result.success) {
            document.getElementById('levelNumber').value = result.next_level;
            document.getElementById('gameCategory').value = '';
            openModal('addLevelModal');
        } else {
            showNotification('Failed to get next level number: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error getting next level:', error);
        showNotification('Error getting next level number', 'error');
    }
}

// Add Level
async function addLevel() {
    const levelNumber = document.getElementById('levelNumber').value;
    const gameCategory = document.getElementById('gameCategory').value;

    if (!levelNumber || !gameCategory) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=add_level`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                level_number: parseInt(levelNumber),
                game_category: gameCategory
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            closeModal('addLevelModal');
            // Refresh the levels display
            await loadGameContent();
            displayGameLevels();
        } else {
            showNotification('Failed to create level: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error creating level:', error);
        showNotification('Error creating level', 'error');
    }
}

// Show Delete Level Modal
function showDeleteLevelModal(levelNumber) {
    // Find the level data
    const level = gameLevels.find(l => l.levelID === levelNumber);
    if (!level) {
        showNotification('Level not found', 'error');
        return;
    }

    // Update modal content
    document.getElementById('deleteLevelNumber').textContent = levelNumber;
    document.getElementById('deleteFileName').textContent = levelNumber;
    document.getElementById('deleteQuestionCount').textContent = level.questions.length;

    // Store level number for confirmation
    document.getElementById('confirmDeleteBtn').setAttribute('data-level', levelNumber);

    // Show modal
    openModal('deleteLevelModal');
}

// Confirm Delete Level (called from modal button)
function confirmDeleteLevel() {
    const levelNumber = document.getElementById('confirmDeleteBtn').getAttribute('data-level');
    if (levelNumber) {
        deleteLevel(parseInt(levelNumber));
        closeModal('deleteLevelModal');
    }
}

// Delete Level
async function deleteLevel(levelNumber) {
    try {
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=delete_level&level_number=${levelNumber}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            // Refresh the levels display
            await loadGameContent();
            displayGameLevels();
        } else {
            showNotification('Failed to delete level: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error deleting level:', error);
        showNotification('Error deleting level', 'error');
    }
}

// Level Rearrangement Functions

// Show Rearrange Levels Modal
function showRearrangeLevelsModal() {
    // Populate swap dropdowns
    populateSwapDropdowns();

    // Populate insert levels
    populateInsertLevels();

    // Populate move questions dropdowns
    populateMoveQuestionsDropdowns();

    // Show modal
    openModal('rearrangeLevelsModal');
}

// Populate move questions dropdowns - Updated to handle missing elements gracefully
function populateMoveQuestionsDropdowns() {
    const sourceLevel = document.getElementById('sourceQuestionLevel');
    const targetLevel = document.getElementById('targetQuestionLevel');

    // Check if elements exist (they may have been removed from the UI)
    if (!sourceLevel || !targetLevel) {
        // Elements don't exist, skip population (this is expected after UI changes)
        return;
    }

    // Clear existing options
    sourceLevel.innerHTML = '<option value="">Select source level...</option>';
    targetLevel.innerHTML = '<option value="">Select target level...</option>';

    // Sort levels by level number
    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);

    // Populate both dropdowns
    sortedLevels.forEach(level => {
        // Only show levels that have questions for source
        if (level.questions.length > 0) {
            const sourceOption = document.createElement('option');
            sourceOption.value = level.levelID;
            sourceOption.textContent = `Level ${level.levelID} (${level.questions.length} questions)`;
            sourceLevel.appendChild(sourceOption);
        }

        // Show all levels for target (including empty ones)
        const targetOption = document.createElement('option');
        targetOption.value = level.levelID;
        targetOption.textContent = `Level ${level.levelID} (${level.questions.length} questions)`;
        targetLevel.appendChild(targetOption);
    });
}

// Populate swap dropdowns with available levels
function populateSwapDropdowns() {
    const swap1 = document.getElementById('swapLevel1');
    const swap2 = document.getElementById('swapLevel2');

    // Clear existing options
    swap1.innerHTML = '<option value="">Select level...</option>';
    swap2.innerHTML = '<option value="">Select level...</option>';

    // Add level options
    gameLevels.forEach(level => {
        const option1 = document.createElement('option');
        option1.value = level.levelID;
        option1.textContent = `Level ${level.levelID} (${level.questions.length} questions)`;
        swap1.appendChild(option1);

        const option2 = document.createElement('option');
        option2.value = level.levelID;
        option2.textContent = `Level ${level.levelID} (${level.questions.length} questions)`;
        swap2.appendChild(option2);
    });
}

// Populate levels for insert before/after functionality
function populateInsertLevels() {
    const container = document.getElementById('insertLevels');
    container.innerHTML = '';

    // Sort levels by level number
    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);

    sortedLevels.forEach((level, index) => {
        const levelItem = document.createElement('div');
        levelItem.className = 'insert-level-item';
        levelItem.dataset.levelId = level.levelID;

        // Check if this level is in its original position
        const originalPosition = level.levelID;
        const currentPosition = index + 1;
        const isRearranged = originalPosition !== currentPosition;

        levelItem.innerHTML = `
            <div class="level-item-info">
                <div class="level-item-details">
                    <div class="level-item-title">
                        ${level.levelName || `Level ${level.levelID}`}
                        ${isRearranged ? '<span class="rearranged-indicator" title="This level has been moved from its original position"><i class="fas fa-exchange-alt"></i></span>' : ''}
                    </div>
                    <div class="level-item-meta">
                        ${level.questions.length} questions
                        ${isRearranged ? `<span class="original-position-text">Originally Level ${originalPosition}</span>` : ''}
                    </div>
                </div>
            </div>
            <div class="level-position-indicator ${isRearranged ? 'rearranged' : ''}">
                Position ${currentPosition}
                ${isRearranged ? '<i class="fas fa-info-circle rearranged-info" title="Level has been rearranged"></i>' : ''}
            </div>
            <div class="level-actions">
                <div class="position-controls">
                    <label for="movePositions_${level.levelID}">Positions:</label>
                    <input type="number" id="movePositions_${level.levelID}" min="1" max="${gameLevels.length - 1}" value="1" class="position-input">
                </div>
                <button class="btn-small btn-primary" onclick="moveUpByPositions(${level.levelID})" title="Move Up">
                    <i class="fas fa-angle-up"></i>
                </button>
                <button class="btn-small btn-primary" onclick="moveDownByPositions(${level.levelID})" title="Move Down">
                    <i class="fas fa-angle-down"></i>
                </button>
            </div>
        `;

        container.appendChild(levelItem);
    });
}

// Level Movement Functions

// Move level up by specified positions
async function moveUpByPositions(levelId) {
    const positionsInput = document.getElementById(`movePositions_${levelId}`);
    const positions = parseInt(positionsInput.value) || 1;
    const currentPosition = getCurrentLevelPosition(levelId);
    const targetPosition = Math.max(1, currentPosition - positions);

    if (targetPosition !== currentPosition) {
        await moveLevelToPosition(levelId, targetPosition);
    } else {
        showNotification('Level is already at the top or cannot move further up', 'info');
    }
}

// Move level down by specified positions
async function moveDownByPositions(levelId) {
    const positionsInput = document.getElementById(`movePositions_${levelId}`);
    const positions = parseInt(positionsInput.value) || 1;
    const currentPosition = getCurrentLevelPosition(levelId);
    const maxPosition = gameLevels.length;
    const targetPosition = Math.min(maxPosition, currentPosition + positions);

    if (targetPosition !== currentPosition) {
        await moveLevelToPosition(levelId, targetPosition);
    } else {
        showNotification('Level is already at the bottom or cannot move further down', 'info');
    }
}

// Get current position of a level
function getCurrentLevelPosition(levelId) {
    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    return sortedLevels.findIndex(level => level.levelID === levelId) + 1;
}

// Move level to specific position
async function moveLevelToPosition(levelId, targetPosition) {
    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    const currentIndex = sortedLevels.findIndex(level => level.levelID === levelId);
    const currentPosition = currentIndex + 1;

    if (currentPosition === targetPosition) {
        showNotification('Level is already in that position', 'info');
        return;
    }

    // Create a simple swap with the level at target position
    const targetIndex = targetPosition - 1;
    if (targetIndex >= 0 && targetIndex < sortedLevels.length) {
        const targetLevelId = sortedLevels[targetIndex].levelID;

        // Simple swap between current level and target level
        const levelMappings = [
            { old_level: levelId, new_level: targetLevelId },
            { old_level: targetLevelId, new_level: levelId }
        ];

        await applyLevelMappings(levelMappings);
    }
}

// Perform quick swap
async function performQuickSwap() {
    const level1 = document.getElementById('swapLevel1').value;
    const level2 = document.getElementById('swapLevel2').value;

    if (!level1 || !level2) {
        showNotification('Please select both levels to swap', 'error');
        return;
    }

    if (level1 === level2) {
        showNotification('Cannot swap a level with itself', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=swap_levels`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                level1: parseInt(level1),
                level2: parseInt(level2)
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            closeModal('rearrangeLevelsModal');
            // Refresh the levels display
            await loadGameContent();
            displayGameLevels();
        } else {
            showNotification('Failed to swap levels: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error swapping levels:', error);
        showNotification('Error swapping levels', 'error');
    }
}

// Apply level mappings to the database
async function applyLevelMappings(levelMappings) {
    if (levelMappings.length === 0) {
        showNotification('No changes detected in level order', 'info');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=rearrange_levels`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                level_mappings: levelMappings
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            // Refresh the levels display
            await loadGameContent();
            displayGameLevels();
            populateInsertLevels();
            populateMoveQuestionsDropdowns();
        } else {
            showNotification('Failed to rearrange levels: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error rearranging levels:', error);
        showNotification('Error rearranging levels', 'error');
    }
}

// Move Questions Between Levels Functions

// Show move questions modal - Updated to handle missing elements
function showMoveQuestionsModal() {
    const sourceLevelElement = document.getElementById('sourceQuestionLevel');
    const targetLevelElement = document.getElementById('targetQuestionLevel');

    // Check if elements exist (they may have been removed from the UI)
    if (!sourceLevelElement || !targetLevelElement) {
        showNotification('Move questions functionality has been moved to the "View All Questions" interface', 'info');
        return;
    }

    const sourceLevel = parseInt(sourceLevelElement.value);
    const targetLevel = parseInt(targetLevelElement.value);

    if (!sourceLevel || !targetLevel) {
        showNotification('Please select both source and target levels', 'error');
        return;
    }

    if (sourceLevel === targetLevel) {
        showNotification('Source and target levels cannot be the same', 'error');
        return;
    }

    // Find the levels
    const sourceLevelData = gameLevels.find(l => l.levelID === sourceLevel);
    const targetLevelData = gameLevels.find(l => l.levelID === targetLevel);

    if (!sourceLevelData || !targetLevelData) {
        showNotification('Selected levels not found', 'error');
        return;
    }

    if (sourceLevelData.questions.length === 0) {
        showNotification('Source level has no questions to move', 'error');
        return;
    }

    // Update modal information
    document.getElementById('sourceLevelName').textContent = `Level ${sourceLevel}`;
    document.getElementById('sourceLevelQuestionCount').textContent = sourceLevelData.questions.length;
    document.getElementById('targetLevelName').textContent = `Level ${targetLevel}`;
    document.getElementById('targetLevelQuestionCount').textContent = targetLevelData.questions.length;

    // Populate questions list
    populateQuestionsForMove(sourceLevelData);

    // Show modal
    openModal('moveQuestionsModal');
}

// Populate questions list for moving
function populateQuestionsForMove(sourceLevel) {
    const container = document.getElementById('moveQuestionsContainer');
    container.innerHTML = '';

    sourceLevel.questions.forEach((question, index) => {
        const questionItem = document.createElement('div');
        questionItem.className = 'move-question-item';
        questionItem.dataset.questionId = question.id;

        const questionText = question.question.length > 100
            ? question.question.substring(0, 100) + '...'
            : question.question;

        questionItem.innerHTML = `
            <div class="question-checkbox">
                <input type="checkbox" id="question-${question.id}" onchange="updateSelectedCount()">
            </div>
            <div class="question-info">
                <div class="question-number">Q${index + 1}</div>
                <div class="question-text">${questionText}</div>
                <div class="question-type">${question.quizType || 'Multiple Choice'}</div>
            </div>
        `;

        container.appendChild(questionItem);
    });

    updateSelectedCount();
}

// Select all questions
function selectAllQuestions() {
    const checkboxes = document.querySelectorAll('#moveQuestionsContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

// Deselect all questions
function deselectAllQuestions() {
    const checkboxes = document.querySelectorAll('#moveQuestionsContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

// Update selected count
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('#moveQuestionsContainer input[type="checkbox"]');
    const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    document.querySelector('.selected-count').textContent = `${selectedCount} questions selected`;

    const moveBtn = document.getElementById('moveQuestionsBtn');
    moveBtn.disabled = selectedCount === 0;
}

// Execute question move - Updated to handle missing elements
async function executeQuestionMove() {
    const sourceLevelElement = document.getElementById('sourceQuestionLevel');
    const targetLevelElement = document.getElementById('targetQuestionLevel');

    // Check if elements exist (they may have been removed from the UI)
    if (!sourceLevelElement || !targetLevelElement) {
        showNotification('Move questions functionality has been moved to the "View All Questions" interface', 'info');
        return;
    }

    const sourceLevel = parseInt(sourceLevelElement.value);
    const targetLevel = parseInt(targetLevelElement.value);

    const checkboxes = document.querySelectorAll('#moveQuestionsContainer input[type="checkbox"]:checked');
    const questionIds = Array.from(checkboxes).map(cb => {
        return parseInt(cb.id.replace('question-', ''));
    });

    if (questionIds.length === 0) {
        showNotification('No questions selected', 'error');
        return;
    }

    if (!confirm(`Move ${questionIds.length} question(s) from Level ${sourceLevel} to Level ${targetLevel}?`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=move_questions`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                question_ids: questionIds,
                target_level: targetLevel
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Successfully moved ${questionIds.length} question(s) to Level ${targetLevel}`, 'success');
            closeModal('moveQuestionsModal');
            // Refresh the data
            await loadGameContent();
            displayGameLevels();
            populateMoveQuestionsDropdowns();
        } else {
            showNotification('Failed to move questions: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error moving questions:', error);
        showNotification('Error moving questions', 'error');
    }
}

// Bulk Operations

// Reverse all levels
async function reverseAllLevels() {
    if (!confirm('Are you sure you want to reverse the order of all levels?')) {
        return;
    }

    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    const levelMappings = [];

    sortedLevels.forEach((level, index) => {
        const newPosition = sortedLevels.length - index;
        if (level.levelID !== newPosition) {
            levelMappings.push({
                old_level: level.levelID,
                new_level: newPosition
            });
        }
    });

    if (levelMappings.length > 0) {
        await applyLevelMappings(levelMappings);
    } else {
        showNotification('Levels are already in reverse order', 'info');
    }
}

// Sort levels by question count
async function sortLevelsByQuestions() {
    if (!confirm('Are you sure you want to sort levels by question count (ascending)?')) {
        return;
    }

    const sortedByQuestions = [...gameLevels].sort((a, b) => a.questions.length - b.questions.length);
    const levelMappings = [];

    sortedByQuestions.forEach((level, index) => {
        const newPosition = index + 1;
        if (level.levelID !== newPosition) {
            levelMappings.push({
                old_level: level.levelID,
                new_level: newPosition
            });
        }
    });

    if (levelMappings.length > 0) {
        await applyLevelMappings(levelMappings);
    } else {
        showNotification('Levels are already sorted by question count', 'info');
    }
}

// Compact levels (remove gaps in numbering)
async function compactLevels() {
    if (!confirm('Are you sure you want to compact levels and remove gaps in numbering?')) {
        return;
    }

    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    const levelMappings = [];

    sortedLevels.forEach((level, index) => {
        const newPosition = index + 1;
        if (level.levelID !== newPosition) {
            levelMappings.push({
                old_level: level.levelID,
                new_level: newPosition
            });
        }
    });

    if (levelMappings.length > 0) {
        await applyLevelMappings(levelMappings);
    } else {
        showNotification('Levels are already compacted', 'info');
    }
}
