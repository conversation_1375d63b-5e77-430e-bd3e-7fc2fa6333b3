/* Global Scrollbar Styling - Consistent with Edit Question Modal Design */
*::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

*::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
    background: rgba(52, 152, 219, 0.5);
    border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
    background: rgba(52, 152, 219, 0.7);
}

/* Firefox scrollbar styling */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(52, 152, 219, 0.5) transparent;
}

/* Base Styles - Matching progress page */
:root {
    --bg-dark: rgb(11, 8, 16);
    --bg-darker: rgb(5, 3, 10);
    --bg-light: rgb(30, 25, 40);
    --primary: #8a63f2;
    --primary-dark: #6a4bc7;
    --secondary: #ff9e3f;
    --success: #6fcf97;
    --warning: #f2c94c;
    --danger: #eb5757;
    --light: rgba(255, 255, 255, 0.9);
    --dark: rgba(0, 0, 0, 0.8);
    --gray: rgba(255, 255, 255, 0.1);
    --dark-gray: rgba(255, 255, 255, 0.4);
    --text: rgba(255, 255, 255, 0.9);
    --text-secondary: rgba(255, 255, 255, 0.7);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--bg-dark);
    color: var(--text);
    line-height: 1.6;
    min-height: 100vh;
    padding: 2rem;
    animation: visibility .5s 1;
}

@keyframes visibility{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

/* Game Container */
.game-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
    background: var(--bg-light);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.game-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--gray);
}

.game-header h1 {
    color: white;
    margin-bottom: 1rem;
    font-size: 2.5rem;
    font-family: "Cinzel", "sans-serif";
    letter-spacing: 1px;
}

/* Accounts Editor Layout */
.accounts-editor {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Form Section */
.accounts-form {
    display: flex;
    flex-direction: column;
    gap: 1.8rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
}

.form-group label {
    font-weight: 600;
    color: white;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.field-note {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.2rem;
}

.input-with-badge {
    position: relative;
}

.input-with-badge input {
    width: 100%;
    padding: 0.9rem 1rem;
    background-color: var(--bg-darker);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    color: var(--text);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.input-with-badge input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(138, 99, 242, 0.2),
                inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.input-with-badge input:read-only {
    background-color: rgba(255, 255, 255, 0.05);
    cursor: not-allowed;
}

.input-badge {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.input-badge.complete {
    background-color: var(--success);
    color: var(--dark);
}

.input-badge.incomplete {
    background-color: var(--warning);
    color: var(--dark);
}

/* Buttons */
.btn-save,
.btn-cancel {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.btn-save {
    background-color: var(--success);
    color: var(--dark);
    box-shadow: 0 4px 15px rgba(111, 207, 151, 0.3);
}

.btn-save:hover {
    background-color: #5dbb81;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(111, 207, 151, 0.4);
}

.btn-cancel {
    background-color: var(--bg-darker);
    color: var(--text);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel:hover {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.password-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.password-wrapper input {
    width: 100%;
    padding: 0.9rem 1rem;
    background-color: var(--bg-darker);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    color: var(--text);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    padding-right: 85px; /* Space for the button */
}

.password-wrapper input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(138, 99, 242, 0.2),
                inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.password-wrapper input:read-only {
    background-color: rgba(255, 255, 255, 0.05);
    cursor: not-allowed;
}

.btn-show {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    padding: 0.5rem 1rem;
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-show:hover {
    background-color: var(--primary-dark);
}

.forgot-password {
    color: var(--primary);
    text-decoration: none;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: inline-block;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.7);
}

.modal-content {
    background-color: var(--bg-light);
    margin: 15% auto;
    padding: 2rem;
    border: 1px solid var(--gray);
    width: 80%;
    max-width: 500px;
    border-radius: 12px;
    text-align: center;
    position: relative;
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    color: var(--text-secondary);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-btn:hover,
.close-btn:focus {
    color: var(--light);
    text-decoration: none;
}

.modal-content h2 {
    margin-bottom: 1rem;
}

.modal-content p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.modal-content input {
    width: calc(100% - 2rem);
    padding: 0.8rem;
    margin-bottom: 1.5rem;
    background: var(--bg-darker);
    border: 1px solid var(--gray);
    border-radius: 6px;
    color: var(--text);
}

.btn-submit {
    padding: 0.8rem 2rem;
    background-color: var(--success);
    color: var(--dark);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-submit:hover {
    background-color: #5dbb81;
}

.error-message {
    color: var(--danger);
    margin-top: 1rem;
    height: 1rem;
}

@media (max-width: 768px) {
    body {
        padding: 1rem;
    }
    
    .game-container {
        padding: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

/* X Button Styles */
.x-button {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: var(--dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--gray);
}

.x-button:hover {
    background-color: var(--danger);
    transform: scale(1.1);
}

.x-button img {
    width: 20px;
    height: 20px;
    filter: invert(1);
}
