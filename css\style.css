@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap');

/* Global Scrollbar Styling - Consistent with Edit Question Modal Design */
*::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

*::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
    background: rgba(52, 152, 219, 0.5);
    border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
    background: rgba(52, 152, 219, 0.7);
}

/* Firefox scrollbar styling */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(52, 152, 219, 0.5) transparent;
}

body{
    background-color: rgb(11, 8, 16);
    animation: visibility .5s 1 linear;
}

@keyframes visibility {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.header{
    background-color: transparent;
    display: flex;
    min-width: 100%;
    min-height: 20vh;
    margin-top: 2%;
}

.header-cont {
    display: flex;
    justify-content: space-between;
    min-width: 100%;
    padding: 0;
    margin: 0;
}

.header .header-cont .title{
    padding-top: 10px;
    padding-left: 8%;
}

.header-cont .title p{
    color: white;
    font-size: 4em;
    font-family: "Cinzel", "sans-serif";
    padding: 25px 0px;
    margin: 0px;
    text-shadow: 0px 0px 5px rgb(255, 255, 255), 0px 0px 15px rgb(255, 255, 255);
}

.header .header-cont .login {
    background-color: transparent;
    font-family: "Cinzel", "sans-serif";
    border: 2px white solid;
    border-radius: 5px;
    color: white;
    padding: 10px 10px;
    height: fit-content;
    margin-right: 2.5%;
    margin-top: 1%;
    cursor: pointer;
}


.body {

    display: flex;
    justify-content: center;
    align-items: end;
    height: 50vh;
}

.body-cont-anim {
    border-radius: 24px;
    margin: 4px;
    height: fit-content;
    padding: 5vw;
    width: 40vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgb(11, 8, 16);
    position: relative;
}

.body-cont-anim::after ,.body-cont-anim::before{
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: -1;
    background-image: conic-gradient( from var(--angle), transparent, rgb(255, 255, 255));
    padding: 1px;
    border-radius: 24px;
    animation: rotate 3s linear infinite;
}

.body-cont-anim::before {
    filter: blur(15px);
}

/* ANGLE PROPERTY */
@property --angle {
    syntax: "<angle>";
    initial-value: 0deg;
    inherits: false;
}

@keyframes rotate {
    0% {
        --angle: 0deg;
    }

    100% {
        --angle: 360deg;
    }
}

.body-cont-anim button {
    height: fit-content;
    padding: 10px;
    border: rgb(255, 255, 255) 1px solid;
    color: rgb(255,255,255);
    border-radius: 4px;
    font-family: "Cinzel", "sans-serif";
    background-color: transparent;
    cursor: pointer;
}

.footer {

    height: 20vh;
    display: flex;

}